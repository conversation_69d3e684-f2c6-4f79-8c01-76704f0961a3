using System.Threading;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Checkpoint;
using TryCode.DatabaseMigration.Checkpoint.Models;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Reports;

namespace TryCode.DatabaseMigration.Parallel
{
    /// <summary>
    /// 检查点管理器适配器
    /// </summary>
    public class CheckpointManagerAdapter : ICheckpointManager
    {
        private readonly CheckpointManager _checkpointManager;

        /// <summary>
        /// 初始化新的<see cref="CheckpointManagerAdapter"/>实例
        /// </summary>
        /// <param name="checkpointManager">检查点管理器</param>
        public CheckpointManagerAdapter(CheckpointManager checkpointManager)
        {
            _checkpointManager = checkpointManager;
        }

        /// <summary>
        /// 初始化迁移检查点
        /// </summary>
        public async Task InitializeMigrationCheckpointAsync(int totalTables, CancellationToken cancellationToken = default)
        {
            // 使用实际的InitializeAsync方法
            var checkpoint = await _checkpointManager.InitializeAsync("MySQL", "MySQL", cancellationToken);
            checkpoint.TotalTables = totalTables;
        }

        /// <summary>
        /// 标记表为进行中
        /// </summary>
        public async Task MarkTableInProgressAsync(string tableName, CancellationToken cancellationToken = default)
        {
            var tableCheckpoint = await _checkpointManager.GetTableCheckpointAsync(tableName, cancellationToken);

            // 保存原始的SchemaOnly标志
            bool schemaOnly = tableCheckpoint.SchemaOnly;

            tableCheckpoint.Status = "InProgress";

            // 恢复SchemaOnly标志
            tableCheckpoint.SchemaOnly = schemaOnly;

            await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);
        }

        /// <summary>
        /// 更新表的行数
        /// </summary>
        public async Task UpdateTableRowsAsync(string tableName, long totalRows, long migratedRows, string stage, CancellationToken cancellationToken = default)
        {
            var tableCheckpoint = await _checkpointManager.GetTableCheckpointAsync(tableName, cancellationToken);
            tableCheckpoint.TotalRows = totalRows;
            tableCheckpoint.MigratedRows = migratedRows;

            // 根据阶段设置表的不同标志
            if (stage == "表结构创建")
            {
                tableCheckpoint.SchemaCreated = false;
                tableCheckpoint.ForeignKeysCreated = false;
            }
            else if (stage == "数据迁移")
            {
                tableCheckpoint.SchemaCreated = true;
                tableCheckpoint.ForeignKeysCreated = false;
            }
            else if (stage == "外键创建")
            {
                tableCheckpoint.SchemaCreated = true;
                tableCheckpoint.ForeignKeysCreated = true;
            }

            await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);
        }

        /// <summary>
        /// 标记表为已完成
        /// </summary>
        public async Task MarkTableCompletedAsync(string tableName, CancellationToken cancellationToken = default)
        {
            await _checkpointManager.MarkTableCompletedAsync(tableName, cancellationToken);
        }

        /// <summary>
        /// 标记表为失败
        /// </summary>
        public async Task MarkTableFailedAsync(string tableName, string error, CancellationToken cancellationToken = default)
        {
            await _checkpointManager.MarkTableFailedAsync(tableName, error, cancellationToken);
        }

        /// <summary>
        /// 标记表为已跳过
        /// </summary>
        public async Task MarkTableSkippedAsync(string tableName, string reason, CancellationToken cancellationToken = default)
        {
            var tableCheckpoint = await _checkpointManager.GetTableCheckpointAsync(tableName, cancellationToken);
            tableCheckpoint.Status = "Skipped";
            tableCheckpoint.ErrorMessage = reason;
            await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);
        }

        /// <summary>
        /// 获取迁移检查点
        /// </summary>
        public async Task<Core.Reports.MigrationCheckpoint> GetMigrationCheckpointAsync(CancellationToken cancellationToken = default)
        {
            var checkpoint = await _checkpointManager.InitializeAsync("MySQL", "MySQL", cancellationToken);
            return ConvertToCore(checkpoint);
        }

        /// <summary>
        /// 保存检查点
        /// </summary>
        public async Task SaveCheckpointAsync(CancellationToken cancellationToken = default)
        {
            // _checkpointManager在每次更新后会自动保存
            await Task.CompletedTask;
        }

        /// <summary>
        /// 获取表的断点信息
        /// </summary>
        public async Task<Checkpoint.Models.TableCheckpoint> GetTableCheckpointAsync(string tableName, CancellationToken cancellationToken = default)
        {
            return await _checkpointManager.GetTableCheckpointAsync(tableName, cancellationToken);
        }

        /// <summary>
        /// 更新表的断点信息
        /// </summary>
        public async Task UpdateTableCheckpointAsync(Checkpoint.Models.TableCheckpoint tableCheckpoint, CancellationToken cancellationToken = default)
        {
            await _checkpointManager.UpdateTableCheckpointAsync(tableCheckpoint, cancellationToken);
        }

        /// <summary>
        /// 将Checkpoint模型转换为Core模型
        /// </summary>
        private Core.Reports.MigrationCheckpoint ConvertToCore(Checkpoint.Models.MigrationCheckpoint checkpoint)
        {
            var result = new Core.Reports.MigrationCheckpoint
            {
                TotalTables = checkpoint.TotalTables
            };

            foreach (var tableEntry in checkpoint.TableCheckpoints)
            {
                var tableName = tableEntry.Key;
                var tableCheckpoint = tableEntry.Value;

                var status = GetTableStatus(tableCheckpoint.Status);

                result.TableCheckpoints[tableName] = new Core.Reports.TableCheckpoint
                {
                    TotalRows = tableCheckpoint.TotalRows,
                    MigratedRows = tableCheckpoint.MigratedRows,
                    Status = status,
                    Error = tableCheckpoint.ErrorMessage,
                    Stage = tableCheckpoint.ForeignKeysCreated ? "外键创建" :
                           tableCheckpoint.SchemaCreated ? "数据迁移" : "表结构创建"
                };
            }

            return result;
        }

        /// <summary>
        /// 获取表状态
        /// </summary>
        private TableStatus GetTableStatus(string status)
        {
            return status switch
            {
                "NotStarted" => TableStatus.Pending,
                "InProgress" => TableStatus.InProgress,
                "Completed" => TableStatus.Completed,
                "Failed" => TableStatus.Failed,
                "Skipped" => TableStatus.Skipped,
                _ => TableStatus.Pending
            };
        }
    }
}
