using System;
using System.IO;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using TryCode.DatabaseMigration.Checkpoint.Models;

namespace TryCode.DatabaseMigration.Checkpoint
{
    /// <summary>
    /// 断点管理器
    /// </summary>
    public class CheckpointManager
    {
        private readonly string _checkpointFilePath;
        private readonly SemaphoreSlim _semaphore;
        private MigrationCheckpoint _checkpoint;

        public CheckpointManager(string checkpointFilePath)
        {
            _checkpointFilePath = checkpointFilePath ?? throw new ArgumentNullException(nameof(checkpointFilePath));
            _semaphore = new SemaphoreSlim(1, 1);
        }

        /// <summary>
        /// 初始化或加载断点信息
        /// </summary>
        public virtual async Task<MigrationCheckpoint> InitializeAsync(
            string sourceType,
            string targetType,
            CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (File.Exists(_checkpointFilePath))
                {
                    var json = await File.ReadAllTextAsync(_checkpointFilePath, cancellationToken);
                    _checkpoint = JsonSerializer.Deserialize<MigrationCheckpoint>(json);

                    // 验证断点信息的有效性
                    if (_checkpoint.SourceType != sourceType || _checkpoint.TargetType != targetType)
                    {
                        throw new InvalidOperationException("断点文件与当前迁移配置不匹配");
                    }
                }
                else
                {
                    _checkpoint = new MigrationCheckpoint
                    {
                        SourceType = sourceType,
                        TargetType = targetType
                    };
                    await SaveCheckpointAsync(cancellationToken);
                }

                return _checkpoint;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 获取表的断点信息
        /// </summary>
        public virtual async Task<TableCheckpoint> GetTableCheckpointAsync(
            string tableName,
            CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_checkpoint == null)
                {
                    throw new InvalidOperationException("断点管理器尚未初始化");
                }

                if (!_checkpoint.TableCheckpoints.TryGetValue(tableName, out var tableCheckpoint))
                {
                    tableCheckpoint = new TableCheckpoint
                    {
                        TableName = tableName,
                        Status = "NotStarted",
                        StartTime = DateTime.Now,
                        LastUpdateTime = DateTime.Now
                    };
                    _checkpoint.TableCheckpoints[tableName] = tableCheckpoint;
                    await SaveCheckpointAsync(cancellationToken);
                }

                return tableCheckpoint;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 更新表的断点信息
        /// </summary>
        public virtual async Task UpdateTableCheckpointAsync(
            TableCheckpoint tableCheckpoint,
            CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_checkpoint == null)
                {
                    throw new InvalidOperationException("断点管理器尚未初始化");
                }

                tableCheckpoint.LastUpdateTime = DateTime.Now;
                _checkpoint.TableCheckpoints[tableCheckpoint.TableName] = tableCheckpoint;

                // 更新总体进度
                UpdateProgress();

                await SaveCheckpointAsync(cancellationToken);
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 标记表迁移完成
        /// </summary>
        public virtual async Task MarkTableCompletedAsync(
            string tableName,
            CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_checkpoint == null)
                {
                    throw new InvalidOperationException("断点管理器尚未初始化");
                }

                if (_checkpoint.TableCheckpoints.TryGetValue(tableName, out var tableCheckpoint))
                {
                    tableCheckpoint.Status = "Completed";
                    tableCheckpoint.LastUpdateTime = DateTime.Now;
                    _checkpoint.CompletedTables++;

                    // 检查是否所有表都已完成
                    if (_checkpoint.CompletedTables == _checkpoint.TotalTables)
                    {
                        _checkpoint.Status = "Completed";
                    }

                    await SaveCheckpointAsync(cancellationToken);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 标记表迁移失败
        /// </summary>
        public virtual async Task MarkTableFailedAsync(
            string tableName,
            string errorMessage,
            CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                if (_checkpoint == null)
                {
                    throw new InvalidOperationException("断点管理器尚未初始化");
                }

                if (_checkpoint.TableCheckpoints.TryGetValue(tableName, out var tableCheckpoint))
                {
                    // 如果错误是关于并发集合修改，并且表已经完成数据迁移，则将状态设置为已完成
                    if (errorMessage.Contains("Operations that change non-concurrent collections") &&
                        tableCheckpoint.TotalRows > 0 && tableCheckpoint.MigratedRows >= tableCheckpoint.TotalRows)
                    {
                        tableCheckpoint.Status = "Completed";
                        tableCheckpoint.ErrorMessage = null;
                        tableCheckpoint.LastUpdateTime = DateTime.Now;
                    }
                    else
                    {
                        tableCheckpoint.Status = "Failed";
                        tableCheckpoint.ErrorMessage = errorMessage;
                        tableCheckpoint.LastUpdateTime = DateTime.Now;
                        _checkpoint.Status = "Failed";
                        _checkpoint.ErrorMessage = $"表 {tableName} 迁移失败: {errorMessage}";
                    }

                    await SaveCheckpointAsync(cancellationToken);
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 更新总体进度
        /// </summary>
        private void UpdateProgress()
        {
            _checkpoint.MigratedRows = 0;
            _checkpoint.TotalRows = 0;
            int completedTables = 0;

            foreach (var tableCheckpoint in _checkpoint.TableCheckpoints.Values)
            {
                _checkpoint.TotalRows += tableCheckpoint.TotalRows;
                _checkpoint.MigratedRows += tableCheckpoint.MigratedRows;

                // 计算已完成的表数量
                if (tableCheckpoint.Status == "Completed" ||
                    (tableCheckpoint.TotalRows > 0 && tableCheckpoint.MigratedRows >= tableCheckpoint.TotalRows))
                {
                    completedTables++;
                }
            }

            // 更新已完成表数量
            _checkpoint.CompletedTables = completedTables;

            // 如果所有表都已完成，则更新状态为已完成
            if (completedTables == _checkpoint.TableCheckpoints.Count && _checkpoint.TableCheckpoints.Count > 0)
            {
                _checkpoint.Status = "Completed";
            }

            _checkpoint.LastUpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 保存断点信息到文件
        /// </summary>
        private async Task SaveCheckpointAsync(CancellationToken cancellationToken)
        {
            var json = JsonSerializer.Serialize(_checkpoint, new JsonSerializerOptions
            {
                WriteIndented = true
            });
            await File.WriteAllTextAsync(_checkpointFilePath, json, cancellationToken);
        }
    }
}
