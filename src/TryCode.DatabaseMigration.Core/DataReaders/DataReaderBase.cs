using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;

namespace TryCode.DatabaseMigration.Core.DataReaders
{
    /// <summary>
    /// 数据读取器基类，提供基础的重试、错误处理和日志记录功能
    /// </summary>
    public abstract class DataReaderBase : IDataReader
    {
        protected readonly string ConnectionString;
        protected readonly ILogger Logger;
        protected readonly int MaxRetries;
        protected readonly int RetryDelaySeconds;

        protected DataReaderBase(string connectionString, ILogger? logger = null, int maxRetries = 3, int retryDelaySeconds = 5)
        {
            ConnectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
            Logger = logger ?? NullLogger.Instance;
            MaxRetries = maxRetries > 0 ? maxRetries : 3;
            RetryDelaySeconds = retryDelaySeconds > 0 ? retryDelaySeconds : 5;
        }

        /// <summary>
        /// 使用重试机制执行数据库操作
        /// </summary>
        protected async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, CancellationToken cancellationToken = default)
        {
            Exception? lastException = null;

            for (int attempt = 0; attempt <= MaxRetries; attempt++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    
                    if (attempt >= MaxRetries)
                    {
                        Logger.LogError(ex, "{OperationName}在{MaxRetries}次尝试后失败", operationName, MaxRetries);
                        throw new Exception($"{operationName}在{MaxRetries}次尝试后操作仍然失败。最后一次错误: {ex.Message}", ex);
                    }
                    
                    var delayTime = TimeSpan.FromSeconds(RetryDelaySeconds * (attempt + 1));
                    Logger.LogWarning(ex, "{OperationName}失败，将在{DelaySeconds}秒后进行第{RetryCount}次重试", 
                        operationName, delayTime.TotalSeconds, attempt + 1);
                    
                    await Task.Delay(delayTime, cancellationToken);
                }
            }

            throw new Exception($"{operationName}失败", lastException);
        }

        #region IDataReader 接口实现

        public abstract Task<List<TableSchema>> GetTableSchemasAsync(CancellationToken cancellationToken = default);

        public abstract Task<TableSchema> GetTableSchemaAsync(string tableName, IEnumerable<string>? excludedColumns = null, CancellationToken cancellationToken = default);

        public abstract Task<IEnumerable<dynamic>> ReadTableDataAsync(string tableName, int pageSize, long offset, CancellationToken cancellationToken = default);

        public abstract Task<long> GetTableRowCountAsync(string tableName, CancellationToken cancellationToken = default);

        #endregion

        #region 受保护的工具方法

        /// <summary>
        /// 验证表名参数
        /// </summary>
        protected void ValidateTableName(string tableName)
        {
            if (string.IsNullOrEmpty(tableName))
            {
                throw new ArgumentNullException(nameof(tableName));
            }
        }

        /// <summary>
        /// 验证分页参数
        /// </summary>
        protected void ValidatePaginationParams(int pageSize, long offset)
        {
            if (pageSize <= 0)
            {
                throw new ArgumentException("页面大小必须大于0", nameof(pageSize));
            }

            if (offset < 0)
            {
                throw new ArgumentException("偏移量不能为负数", nameof(offset));
            }
        }

        #endregion
    }
}
