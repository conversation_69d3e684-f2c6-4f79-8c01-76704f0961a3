using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Nhgdb;
using TryCode.DatabaseMigration.Core.DataReaders;
using TryCode.DatabaseMigration.Core.Interfaces;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.PostgreSQL.Extensions;

namespace TryCode.DatabaseMigration.PostgreSQL
{
    /// <summary>
    /// PostgreSQL数据库读取器，用于从PostgreSQL数据源读取数据结构和内容
    /// </summary>
    public class PostgresDataReader : DataReaderBase, IDbConnectionProvider
    {
        #region 常量

        private const string DefaultSchema = "public";
        private const int DefaultConnectionTimeout = 30;
        private const int DefaultCommandTimeout = 30;
        private const int DefaultKeepAlive = 10;

        #endregion

        #region 私有字段

        private readonly NhgdbConnectionStringBuilder _connectionBuilder;
        private readonly string _schema;

        #endregion

        #region 构造函数

        /// <summary>
        /// 创建PostgreSQL数据读取器实例
        /// </summary>
        /// <param name="connectionString">PostgreSQL数据库连接字符串</param>
        /// <param name="logger">日志记录器(可选)</param>
        /// <param name="maxRetries">最大重试次数(默认:3)</param>
        /// <param name="retryDelaySeconds">重试延迟秒数(默认:5)</param>
        public PostgresDataReader(string connectionString, ILogger<PostgresDataReader>? logger = null, 
            int maxRetries = 3, int retryDelaySeconds = 5)
            : base(connectionString, logger, maxRetries, retryDelaySeconds)
        {
            _connectionBuilder = new NhgdbConnectionStringBuilder(connectionString)
            {
                Timeout = DefaultConnectionTimeout,
                CommandTimeout = DefaultCommandTimeout,
                KeepAlive = DefaultKeepAlive
            };

            // 提取schema，默认为public
            _schema = _connectionBuilder.SearchPath?.Split(',').FirstOrDefault()?.Trim() ?? DefaultSchema;
            
            Logger.LogDebug("PostgreSQL数据读取器已初始化，架构: {Schema}", _schema);
        }

        #endregion

        #region IDbConnectionProvider 实现

        public DbConnection GetConnection()
        {
            return new NhgdbConnection(_connectionBuilder.ConnectionString);
        }

        #endregion

        #region DataReaderBase 抽象方法实现

        public override async Task<TableSchema> GetTableSchemaAsync(string tableName, 
            IEnumerable<string>? excludedColumns = null, CancellationToken cancellationToken = default)
        {
            ValidateTableName(tableName);

            Logger.LogDebug("开始获取表 {TableName} 的结构信息", tableName);

            return await ExecuteWithRetryAsync(async () =>
            {
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                var schema = new TableSchema { Name = tableName };
                var excludedColumnsList = excludedColumns?.ToList() ?? new List<string>();

                // 使用扩展方法获取列信息和分区信息
                await connection.LoadColumnsInfoAsync(schema, excludedColumnsList, _schema, Logger);
                await connection.LoadPartitionInfoAsync(schema, _schema, Logger);

                Logger.LogDebug("表 {TableName} 结构信息获取完成，共有 {ColumnCount} 列，{PrimaryKeyCount} 个主键，{PartitionCount} 个分区",
                    tableName, schema.Columns.Count, schema.PrimaryKeys.Count, schema.Partitions.Count);

                return schema;
            }, $"获取表{tableName}的结构信息", cancellationToken);
        }

        public override async Task<List<TableSchema>> GetTableSchemasAsync(CancellationToken cancellationToken = default)
        {
            Logger.LogInformation("开始获取数据库中所有表的结构信息");

            return await ExecuteWithRetryAsync(async () =>
            {
                var schemas = new Dictionary<string, TableSchema>();
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                // 获取表和列信息
                var columnData = await connection.QueryAsync(
                    @"SELECT 
                        t.table_name,
                        c.column_name,
                        c.data_type,
                        c.is_nullable = 'YES' as is_nullable,
                        c.column_default,
                        c.character_maximum_length,
                        c.numeric_precision,
                        c.numeric_scale,
                        c.is_identity = 'YES' as is_identity,
                        c.udt_name
                    FROM information_schema.tables t
                    JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
                    WHERE t.table_schema = @schema
                    AND t.table_type = 'BASE TABLE'
                    ORDER BY t.table_name, c.ordinal_position",
                    new { schema = _schema });

                // 获取主键信息
                var primaryKeys = await connection.QueryAsync(
                    @"SELECT 
                        tc.table_name,
                        kcu.column_name,
                        tc.constraint_name
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu 
                        ON tc.constraint_name = kcu.constraint_name
                        AND tc.table_schema = kcu.table_schema
                        AND tc.table_name = kcu.table_name
                    WHERE tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_schema = @schema",
                    new { schema = _schema });

                // 获取外键信息
                var foreignKeys = await connection.QueryAsync(
                    @"SELECT
                        tc.table_name,
                        kcu.column_name,
                        ccu.table_name AS referenced_table,
                        ccu.column_name AS referenced_column,
                        tc.constraint_name
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu 
                        ON tc.constraint_name = kcu.constraint_name
                    JOIN information_schema.constraint_column_usage ccu 
                        ON ccu.constraint_name = tc.constraint_name
                    WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_schema = @schema",
                    new { schema = _schema });


                // 处理列信息
                foreach (var column in columnData)
                {
                    var tableName = column.table_name.ToString();
                    if (!schemas.ContainsKey(tableName))
                    {
                        schemas[tableName] = new TableSchema
                        {
                            Name = tableName,
                            Columns = new List<ColumnSchema>(),
                            ForeignKeys = new List<ForeignKeySchema>()
                        };
                    }

                    schemas[tableName].Columns.Add(PostgresDataReaderExtensions.CreateColumnSchema(column));
                }
                
                // 处理主键信息
                foreach (var pk in primaryKeys)
                {
                    var tableName = pk.table_name.ToString();
                    if (schemas.ContainsKey(tableName))
                    {
                        schemas[tableName].AddPrimaryKey(pk.column_name.ToString());
                    }
                }

                // 处理外键信息
                foreach (var fk in foreignKeys)
                {
                    var tableName = fk.table_name.ToString();
                    if (schemas.ContainsKey(tableName))
                    {
                        schemas[tableName].ForeignKeys.Add(new ForeignKeySchema
                        {
                            Name = fk.constraint_name.ToString(),
                            ColumnName = fk.column_name.ToString(),
                            ReferencedTable = fk.referenced_table.ToString(),
                            ReferencedColumn = fk.referenced_column.ToString()
                        });
                    }
                }

                // 加载每个表的分区信息
                foreach (var schema in schemas.Values)
                {
                    await connection.LoadPartitionInfoAsync(schema, _schema, Logger);
                }

                Logger.LogInformation("成功获取了 {TableCount} 个表的结构信息", schemas.Count);
                return new List<TableSchema>(schemas.Values);
            }, "获取所有表的结构信息", cancellationToken);
        }

        public override async Task<IEnumerable<dynamic>> ReadTableDataAsync(string tableName, int pageSize, long offset, 
            CancellationToken cancellationToken = default)
        {
            ValidateTableName(tableName);
            ValidatePaginationParams(pageSize, offset);

            Logger.LogDebug("开始读取表 {TableName} 数据，页大小: {PageSize}, 偏移量: {Offset}", tableName, pageSize, offset);

            return await ExecuteWithRetryAsync(async () =>
            {
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                var sql = $@"SELECT * FROM ""{tableName}"" ORDER BY 1 LIMIT @pageSize OFFSET @offset";
                var data = await connection.QueryAsync(sql, new { pageSize, offset });
                
                Logger.LogDebug("成功从表 {TableName} 读取了 {Count} 行数据", tableName, data.Count());
                return data;
            }, $"从表{tableName}读取数据", cancellationToken);
        }

        public override async Task<long> GetTableRowCountAsync(string tableName, CancellationToken cancellationToken = default)
        {
            ValidateTableName(tableName);

            Logger.LogDebug("开始获取表 {TableName} 的总行数", tableName);

            return await ExecuteWithRetryAsync(async () =>
            {
                using var connection = GetConnection();
                await connection.OpenAsync(cancellationToken);

                var sql = $@"SELECT CAST(COUNT(*) AS bigint) FROM ""{tableName}""";
                var count = await connection.QuerySingleAsync<long>(sql);
                
                Logger.LogDebug("表 {TableName} 共有 {RowCount} 行数据", tableName, count);
                return count;
            }, $"获取表{tableName}的总行数", cancellationToken);
        }

        #endregion
    }
}
