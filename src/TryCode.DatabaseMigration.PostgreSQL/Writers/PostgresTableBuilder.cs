using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Extensions.Logging;
using Nhgdb;
using TryCode.DatabaseMigration.Core.Models;
using TryCode.DatabaseMigration.Core.Writers;

namespace TryCode.DatabaseMigration.PostgreSQL.Writers
{
    /// <summary>
    /// PostgreSQL表结构构建器
    /// </summary>
    public class PostgresTableBuilder : BaseTableBuilder
    {
        private readonly PostgresDataTypeConverter _typeConverter;

        /// <summary>
        /// 初始化PostgreSQL表结构构建器
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="typeConverter">数据类型转换器</param>
        public PostgresTableBuilder(string connectionString, ILogger logger, PostgresDataTypeConverter? typeConverter = null)
            : base(connectionString, logger)
        {
            _typeConverter = typeConverter ?? new PostgresDataTypeConverter(logger);
        }

        /// <summary>
        /// 创建表结构
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <param name="skipSchemaCreation">是否跳过创建表结构</param>
        /// <param name="cancellationToken">取消令牌</param>
        public override async Task CreateTableAsync(TableSchema schema, bool skipSchemaCreation = false, CancellationToken cancellationToken = default)
        {
            if (schema == null)
                throw new ArgumentNullException(nameof(schema));

            // 如果配置为跳过表结构创建，则直接返回
            if (skipSchemaCreation)
            {
                Logger.LogInformation("跳过创建表 {TableName} 的表结构", schema.Name);
                return;
            }

            Logger.LogInformation("开始创建表 {TableName}", schema.Name);
            using var conn = new NhgdbConnection(ConnectionString);
            await conn.OpenAsync(cancellationToken);

            try
            {
                // 检查表是否存在
                bool tableExists = await CheckTableExistsAsync(conn, schema.Name);

                if (tableExists)
                {
                    Logger.LogInformation("表 {TableName} 已存在，将删除后重新创建", schema.Name);
                }

                await DropExistingTable(conn, schema.Name);
                string sqlString = BuildCreateTableSql(schema);
                await ExecuteCreateTableSql(conn, sqlString, schema.Name);
                await ValidateTableCreation(conn, schema.Name);

                // 创建索引
                await CreateIndexes(conn, schema);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "创建表 {TableName} 时发生错误: {ErrorMessage}", schema.Name, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 检查表是否存在
        /// </summary>
        private async Task<bool> CheckTableExistsAsync(NhgdbConnection conn, string tableName)
        {
            return await conn.ExecuteScalarAsync<bool>(
                "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = @tableName)",
                new { tableName });
        }

        private Task DropExistingTable(NhgdbConnection conn, string tableName) =>
            conn.ExecuteAsync($"DROP TABLE IF EXISTS \"{tableName}\" CASCADE");

        private async Task ExecuteCreateTableSql(NhgdbConnection conn, string sqlString, string tableName)
        {
            Logger.LogDebug("生成的建表SQL:\n{SQL}", sqlString);

            try
            {
                await conn.ExecuteAsync(sqlString);
            }
            catch (Exception ex)
            {
                // 记录详细错误信息，包括SQL语句
                throw new Exception($"执行SQL失败: {ex.Message}。\n执行的SQL语句: {sqlString}", ex);
            }
        }

        private async Task ValidateTableCreation(NhgdbConnection conn, string tableName)
        {
            var tableExists = await CheckTableExistsAsync(conn, tableName);

            if (!tableExists)
            {
                throw new Exception($"创建表 {tableName} 失败");
            }

            Logger.LogInformation("表 {TableName} 创建成功", tableName);
        }

        private async Task CreateIndexes(NhgdbConnection conn, TableSchema schema)
        {
            // 创建索引
            foreach (var column in schema.Columns.Where(c => c.IsIndexed && !schema.PrimaryKeys.Contains(c.Name)))
            {
                try
                {
                    var indexName = $"idx_{schema.Name}_{column.Name}";
                    var sql = $"CREATE INDEX \"{indexName}\" ON \"{schema.Name}\" (\"{column.Name}\")";
                    await conn.ExecuteAsync(sql);
                    Logger.LogInformation("已为表 {TableName} 创建索引 {IndexName}", schema.Name, indexName);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "为表 {TableName} 创建索引时出错: {ErrorMessage}", schema.Name, ex.Message);
                }
            }
        }

        /// <summary>
        /// 构建创建表的SQL语句
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>创建表的SQL语句</returns>
        protected override string BuildCreateTableSql(TableSchema schema)
        {
            var sql = new StringBuilder();
            sql.AppendLine($"CREATE TABLE IF NOT EXISTS \"{schema.Name}\" (");

            // 添加列定义
            var columnDefinitions = BuildColumnDefinitions(schema);

            // 添加主键
            var primaryKeyDef = BuildPrimaryKeyDefinition(schema);
            if (!string.IsNullOrEmpty(primaryKeyDef))
            {
                columnDefinitions.Add(primaryKeyDef);
            }

            sql.AppendLine(string.Join(",\n    ", columnDefinitions));
            sql.AppendLine(")");

            sql.AppendLine(";");
            return sql.ToString();
        }

        /// <summary>
        /// 构建列定义
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>列定义列表</returns>
        protected override List<string> BuildColumnDefinitions(TableSchema schema)
        {
            var columnDefinitions = new List<string>();
            var hasSerialColumn = false;

            // 检查是否有自增列
            var autoIncrementColumn = schema.Columns.FirstOrDefault(c =>
                c.IsAutoIncrement ||
                (c.DefaultValue?.Contains("nextval") ?? false) ||
                c.DataType.ToLower() == "serial");

            // 如果有自增列但不是主键，将其添加为主键
            if (autoIncrementColumn != null && !schema.PrimaryKeys.Contains(autoIncrementColumn.Name))
            {
                schema.AddPrimaryKey(autoIncrementColumn.Name);
            }

            foreach (var column in schema.Columns)
            {
                var definition = new StringBuilder();

                // 获取PostgreSQL数据类型
                string pgType;
                bool isSerial = false;

                // 处理自增字段
                var isAutoIncrement = column.IsAutoIncrement ||
                                    (column.DefaultValue?.Contains("nextval") ?? false) ||
                                    column.DataType.ToLower() == "serial";

                if (isAutoIncrement && !hasSerialColumn)
                {
                    hasSerialColumn = true;
                    isSerial = true;

                    // 根据数据类型选择合适的自增类型
                    if (column.DataType.ToLower() == "bigint" || column.DataType.ToLower() == "bigserial")
                    {
                        pgType = "BIGSERIAL";
                    }
                    else
                    {
                        pgType = "SERIAL";
                    }
                }
                else
                {
                    pgType = _typeConverter.ConvertDataType(column);
                }

                definition.Append($"\"{column.Name}\" {pgType}");

                // 添加是否可为空
                if (!isSerial)
                {
                    if (column.IsNullable)
                    {
                        definition.Append(" NULL");
                    }
                    else
                    {
                        definition.Append(" NOT NULL");
                    }

                    // 添加默认值
                    if (!string.IsNullOrEmpty(column.DefaultValue) &&
                        !column.DefaultValue.Contains("nextval") &&
                        !column.DefaultValue.Contains("serial"))
                    {
                        if (column.DataType.ToLower() == "timestamp" &&
                            column.DefaultValue.ToUpper() == "CURRENT_TIMESTAMP")
                        {
                            definition.Append(" DEFAULT CURRENT_TIMESTAMP");
                        }
                        else if (column.DataType.ToLower() == "date" &&
                            column.DefaultValue.ToUpper().Contains("CURRENT_DATE"))
                        {
                            definition.Append(" DEFAULT CURRENT_DATE");
                        }
                        else if (column.DefaultValue.StartsWith("'") && column.DefaultValue.EndsWith("'"))
                        {
                            definition.Append($" DEFAULT {column.DefaultValue}");
                        }
                        else if (decimal.TryParse(column.DefaultValue, out _) ||
                            column.DefaultValue.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                            column.DefaultValue.Equals("false", StringComparison.OrdinalIgnoreCase))
                        {
                            definition.Append($" DEFAULT {column.DefaultValue}");
                        }
                    }
                    else if (column.DataType.ToLower() == "timestamp" && !column.IsNullable)
                    {
                        definition.Append(" DEFAULT CURRENT_TIMESTAMP");
                    }
                }

                columnDefinitions.Add(definition.ToString());
            }

            return columnDefinitions;
        }

        /// <summary>
        /// 构建主键定义
        /// </summary>
        /// <param name="schema">表结构信息</param>
        /// <returns>主键定义</returns>
        protected override string? BuildPrimaryKeyDefinition(TableSchema schema)
        {
            // 添加主键
            if (schema.PrimaryKeys.Any())
            {
                var primaryKeyColumns = schema.PrimaryKeys.Select(pk => $"\"{pk}\"");
                return $"PRIMARY KEY ({string.Join(", ", primaryKeyColumns)})";
            }

            return null;
        }
    }
}
