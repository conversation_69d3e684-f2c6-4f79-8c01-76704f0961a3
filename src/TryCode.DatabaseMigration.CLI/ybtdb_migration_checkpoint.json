{"MigrationId": "eaed921a-193c-4005-8b87-a6ee2b867474", "SourceType": "MySQL", "TargetType": "MySQL", "StartTime": "2025-03-05T18:14:45.485759+08:00", "LastUpdateTime": "2025-03-05T18:14:48.052935+08:00", "Status": "NotStarted", "TotalTables": 0, "CompletedTables": 27, "TotalRows": 763, "MigratedRows": 763, "TableCheckpoints": {"AbpAuditLogs": {"TableName": "AbpAuditLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.431521+08:00", "LastUpdateTime": "2025-03-05T18:14:47.440005+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpAuditLogActions": {"TableName": "AbpAuditLogActions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.433572+08:00", "LastUpdateTime": "2025-03-05T18:14:48.497238+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpClaimTypes": {"TableName": "AbpClaimTypes", "TotalRows": 21, "MigratedRows": 21, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.449582+08:00", "LastUpdateTime": "2025-03-05T18:14:49.498745+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpEntityChanges": {"TableName": "AbpEntityChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.450012+08:00", "LastUpdateTime": "2025-03-05T18:14:50.521127+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpEntityPropertyChanges": {"TableName": "AbpEntityPropertyChanges", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.467775+08:00", "LastUpdateTime": "2025-03-05T18:14:51.53512+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpFeatureValues": {"TableName": "AbpFeature<PERSON><PERSON>ues", "TotalRows": 4, "MigratedRows": 4, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.469515+08:00", "LastUpdateTime": "2025-03-05T18:14:52.536007+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpLinkUsers": {"TableName": "AbpLinkUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.494336+08:00", "LastUpdateTime": "2025-03-05T18:14:53.542079+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpLocalizationLanguages": {"TableName": "AbpLocalizationLanguages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.495596+08:00", "LastUpdateTime": "2025-03-05T18:14:54.544387+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpLocalizationResources": {"TableName": "AbpLocalizationResources", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.504583+08:00", "LastUpdateTime": "2025-03-05T18:14:55.550271+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpLocalizationTexts": {"TableName": "AbpLocalizationTexts", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.513741+08:00", "LastUpdateTime": "2025-03-05T18:14:56.554163+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpOrganizationUnits": {"TableName": "AbpOrganizationUnits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.519715+08:00", "LastUpdateTime": "2025-03-05T18:14:57.610268+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpRoles": {"TableName": "AbpRoles", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.535143+08:00", "LastUpdateTime": "2025-03-05T18:14:58.613861+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpOrganizationUnitRoles": {"TableName": "AbpOrganizationUnitRoles", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.539733+08:00", "LastUpdateTime": "2025-03-05T18:14:59.640611+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpPermissionGrants": {"TableName": "AbpPermissionGrants", "TotalRows": 525, "MigratedRows": 525, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.553431+08:00", "LastUpdateTime": "2025-03-05T18:15:00.642912+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpRoleClaims": {"TableName": "AbpRoleClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.559215+08:00", "LastUpdateTime": "2025-03-05T18:15:01.670687+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpSecurityLogs": {"TableName": "AbpSecurityLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.572211+08:00", "LastUpdateTime": "2025-03-05T18:15:02.673408+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpSettings": {"TableName": "AbpSettings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.575437+08:00", "LastUpdateTime": "2025-03-05T18:15:03.678303+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpTenants": {"TableName": "AbpTenants", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.594514+08:00", "LastUpdateTime": "2025-03-05T18:15:04.680222+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpTenantConnectionStrings": {"TableName": "AbpTenantConnectionStrings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.596585+08:00", "LastUpdateTime": "2025-03-05T18:15:05.721927+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpUsers": {"TableName": "AbpUsers", "TotalRows": 4, "MigratedRows": 4, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.614319+08:00", "LastUpdateTime": "2025-03-05T18:15:06.728799+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpUserClaims": {"TableName": "AbpUserClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.614713+08:00", "LastUpdateTime": "2025-03-05T18:15:07.749429+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpUserLogins": {"TableName": "AbpUserLogins", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.63522+08:00", "LastUpdateTime": "2025-03-05T18:15:08.768656+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpUserOrganizationUnits": {"TableName": "AbpUserOrganizationUnits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.635626+08:00", "LastUpdateTime": "2025-03-05T18:15:09.797214+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpUserRoles": {"TableName": "AbpUserRoles", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.647221+08:00", "LastUpdateTime": "2025-03-05T18:15:10.836028+08:00", "Status": "Completed", "ErrorMessage": null}, "AbpUserTokens": {"TableName": "AbpUserTokens", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.653738+08:00", "LastUpdateTime": "2025-03-05T18:15:11.856359+08:00", "Status": "Completed", "ErrorMessage": null}, "Ybt_DataSources": {"TableName": "Ybt_DataSources", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.673849+08:00", "LastUpdateTime": "2025-03-05T18:15:12.859319+08:00", "Status": "Completed", "ErrorMessage": null}, "ApiDataSet": {"TableName": "ApiDataSet", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.678948+08:00", "LastUpdateTime": "2025-03-05T18:15:13.888372+08:00", "Status": "Completed", "ErrorMessage": null}, "ApiDataSetMappingField": {"TableName": "ApiDataSetMappingField", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.695311+08:00", "LastUpdateTime": "2025-03-05T18:14:46.696525+08:00", "Status": "InProgress", "ErrorMessage": null}, "ApiRequestParameter": {"TableName": "ApiRequestParameter", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.699058+08:00", "LastUpdateTime": "2025-03-05T18:14:46.700563+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppChatGroups": {"TableName": "AppChatGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.714969+08:00", "LastUpdateTime": "2025-03-05T18:14:46.716714+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppGroupChatBlacks": {"TableName": "AppGroupChatBlacks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.717203+08:00", "LastUpdateTime": "2025-03-05T18:14:46.718523+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppGroupMessages": {"TableName": "AppGroupMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.733279+08:00", "LastUpdateTime": "2025-03-05T18:14:46.735633+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppNotificationDefinitionGroups": {"TableName": "AppNotificationDefinitionGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.738494+08:00", "LastUpdateTime": "2025-03-05T18:14:46.739869+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppNotificationDefinitions": {"TableName": "AppNotificationDefinitions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.756606+08:00", "LastUpdateTime": "2025-03-05T18:14:46.760337+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppNotifications": {"TableName": "AppNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.758431+08:00", "LastUpdateTime": "2025-03-05T18:14:46.759845+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformDatas": {"TableName": "AppPlatformDatas", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.77706+08:00", "LastUpdateTime": "2025-03-05T18:14:46.786576+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformDataItems": {"TableName": "AppPlatformDataItems", "TotalRows": 21, "MigratedRows": 21, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.779095+08:00", "LastUpdateTime": "2025-03-05T18:14:46.790229+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformLayouts": {"TableName": "AppPlatformLayouts", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.797426+08:00", "LastUpdateTime": "2025-03-05T18:14:46.805132+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformMenus": {"TableName": "AppPlatformMenus", "TotalRows": 45, "MigratedRows": 45, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.799076+08:00", "LastUpdateTime": "2025-03-05T18:14:46.818891+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformPackages": {"TableName": "AppPlatformPackages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.81944+08:00", "LastUpdateTime": "2025-03-05T18:14:46.820881+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformPackageBlobs": {"TableName": "AppPlatformPackageBlobs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.823751+08:00", "LastUpdateTime": "2025-03-05T18:14:46.825522+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformRoleMenus": {"TableName": "AppPlatformRoleMenus", "TotalRows": 45, "MigratedRows": 45, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.837953+08:00", "LastUpdateTime": "2025-03-05T18:14:46.848715+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformUserFavoriteMenus": {"TableName": "AppPlatformUserFavoriteMenus", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.849463+08:00", "LastUpdateTime": "2025-03-05T18:14:46.852798+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppPlatformUserMenus": {"TableName": "AppPlatformUserMenus", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.859146+08:00", "LastUpdateTime": "2025-03-05T18:14:46.861456+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppUserChatCards": {"TableName": "AppUserChatCards", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.868337+08:00", "LastUpdateTime": "2025-03-05T18:14:46.869752+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppUserChatFriends": {"TableName": "AppUserChatFriends", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.882427+08:00", "LastUpdateTime": "2025-03-05T18:14:46.884398+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppUserChatGroups": {"TableName": "AppUserChatGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.889337+08:00", "LastUpdateTime": "2025-03-05T18:14:46.89083+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppUserChatSettings": {"TableName": "AppUserChatSettings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.899275+08:00", "LastUpdateTime": "2025-03-05T18:14:46.901474+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppUserGroupCards": {"TableName": "AppUserGroupCards", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.906592+08:00", "LastUpdateTime": "2025-03-05T18:14:46.908165+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppUserMessages": {"TableName": "AppUserMessages", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.921176+08:00", "LastUpdateTime": "2025-03-05T18:14:46.922836+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppUserNotifications": {"TableName": "AppUserNotifications", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.925504+08:00", "LastUpdateTime": "2025-03-05T18:14:46.92716+08:00", "Status": "InProgress", "ErrorMessage": null}, "AppUserSubscribes": {"TableName": "AppUserSubscribes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.943203+08:00", "LastUpdateTime": "2025-03-05T18:14:46.944887+08:00", "Status": "InProgress", "ErrorMessage": null}, "SourceDbTableInfo": {"TableName": "SourceDbTableInfo", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.945538+08:00", "LastUpdateTime": "2025-03-05T18:14:46.947806+08:00", "Status": "InProgress", "ErrorMessage": null}, "DbTableField": {"TableName": "DbTableField", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.962724+08:00", "LastUpdateTime": "2025-03-05T18:14:46.965078+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_Companies": {"TableName": "Ybt_Companies", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.963755+08:00", "LastUpdateTime": "2025-03-05T18:14:46.965845+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_Regions": {"TableName": "Ybt_Regions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.986085+08:00", "LastUpdateTime": "2025-03-05T18:14:46.988415+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_Departments": {"TableName": "Ybt_Departments", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:46.986559+08:00", "LastUpdateTime": "2025-03-05T18:14:46.998064+08:00", "Status": "InProgress", "ErrorMessage": null}, "DepartmentClaim": {"TableName": "DepartmentClaim", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.011503+08:00", "LastUpdateTime": "2025-03-05T18:14:47.013581+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerApiResources": {"TableName": "IdentityServerApiResources", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.015071+08:00", "LastUpdateTime": "2025-03-05T18:14:47.022797+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerApiResourceClaims": {"TableName": "IdentityServerApiResourceClaims", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.032147+08:00", "LastUpdateTime": "2025-03-05T18:14:47.042243+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerApiResourceProperties": {"TableName": "IdentityServerApiResourceProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.034716+08:00", "LastUpdateTime": "2025-03-05T18:14:47.035935+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerApiResourceScopes": {"TableName": "IdentityServerApiResourceScopes", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.054174+08:00", "LastUpdateTime": "2025-03-05T18:14:47.061861+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerApiResourceSecrets": {"TableName": "IdentityServerApiResourceSecrets", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.05471+08:00", "LastUpdateTime": "2025-03-05T18:14:47.056188+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerApiScopes": {"TableName": "IdentityServerApiScopes", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.073966+08:00", "LastUpdateTime": "2025-03-05T18:14:47.083951+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerApiScopeClaims": {"TableName": "IdentityServerApiScopeClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.074504+08:00", "LastUpdateTime": "2025-03-05T18:14:47.076638+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerApiScopeProperties": {"TableName": "IdentityServerApiScopeProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.097186+08:00", "LastUpdateTime": "2025-03-05T18:14:47.099064+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClients": {"TableName": "IdentityServerClients", "TotalRows": 4, "MigratedRows": 4, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.10056+08:00", "LastUpdateTime": "2025-03-05T18:14:47.11055+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientClaims": {"TableName": "IdentityServerClientClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.117385+08:00", "LastUpdateTime": "2025-03-05T18:14:47.119115+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientCorsOrigins": {"TableName": "IdentityServerClientCorsOrigins", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.121016+08:00", "LastUpdateTime": "2025-03-05T18:14:47.123835+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientGrantTypes": {"TableName": "IdentityServerClientGrantTypes", "TotalRows": 5, "MigratedRows": 5, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.135666+08:00", "LastUpdateTime": "2025-03-05T18:14:47.144823+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientIdPRestrictions": {"TableName": "IdentityServerClientIdPRestrictions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.150084+08:00", "LastUpdateTime": "2025-03-05T18:14:47.151641+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientPostLogoutRedirectUris": {"TableName": "IdentityServerClientPostLogoutRedirectUris", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.152587+08:00", "LastUpdateTime": "2025-03-05T18:14:47.159787+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientProperties": {"TableName": "IdentityServerClientProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.168673+08:00", "LastUpdateTime": "2025-03-05T18:14:47.171491+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientRedirectUris": {"TableName": "IdentityServerClientRedirectUris", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.170907+08:00", "LastUpdateTime": "2025-03-05T18:14:47.177217+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientScopes": {"TableName": "IdentityServerClientScopes", "TotalRows": 32, "MigratedRows": 32, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.185104+08:00", "LastUpdateTime": "2025-03-05T18:14:47.195454+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerClientSecrets": {"TableName": "IdentityServerClientSecrets", "TotalRows": 4, "MigratedRows": 4, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.188191+08:00", "LastUpdateTime": "2025-03-05T18:14:47.197288+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerDeviceFlowCodes": {"TableName": "IdentityServerDeviceFlowCodes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.20543+08:00", "LastUpdateTime": "2025-03-05T18:14:47.206968+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerIdentityResources": {"TableName": "IdentityServerIdentityResources", "TotalRows": 6, "MigratedRows": 6, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.20817+08:00", "LastUpdateTime": "2025-03-05T18:14:47.215004+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerIdentityResourceClaims": {"TableName": "IdentityServerIdentityResourceClaims", "TotalRows": 21, "MigratedRows": 21, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.223906+08:00", "LastUpdateTime": "2025-03-05T18:14:47.233222+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerIdentityResourceProperties": {"TableName": "IdentityServerIdentityResourceProperties", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.22712+08:00", "LastUpdateTime": "2025-03-05T18:14:47.228649+08:00", "Status": "InProgress", "ErrorMessage": null}, "IdentityServerPersistedGrants": {"TableName": "IdentityServerPersistedGrants", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.243708+08:00", "LastUpdateTime": "2025-03-05T18:14:47.245484+08:00", "Status": "InProgress", "ErrorMessage": null}, "TableDataSet": {"TableName": "TableDataSet", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.246062+08:00", "LastUpdateTime": "2025-03-05T18:14:47.248439+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableDataSetMappingFields": {"TableName": "Ybt_TableDataSetMappingFields", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.261166+08:00", "LastUpdateTime": "2025-03-05T18:14:47.262574+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableInfos": {"TableName": "Ybt_TableInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.263853+08:00", "LastUpdateTime": "2025-03-05T18:14:47.26536+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableFieldGroups": {"TableName": "Ybt_TableFieldGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.281284+08:00", "LastUpdateTime": "2025-03-05T18:14:47.283377+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableFields": {"TableName": "Ybt_TableFields", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.282316+08:00", "LastUpdateTime": "2025-03-05T18:14:47.284682+08:00", "Status": "InProgress", "ErrorMessage": null}, "TableFieldMultiple": {"TableName": "TableFieldMultiple", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.299468+08:00", "LastUpdateTime": "2025-03-05T18:14:47.308564+08:00", "Status": "InProgress", "ErrorMessage": null}, "WF_WorkflowSchemeInfos": {"TableName": "WF_WorkflowSchemeInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.30217+08:00", "LastUpdateTime": "2025-03-05T18:14:47.309706+08:00", "Status": "InProgress", "ErrorMessage": null}, "WF_WorkflowSchemes": {"TableName": "WF_WorkflowSchemes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.332689+08:00", "LastUpdateTime": "2025-03-05T18:14:47.335073+08:00", "Status": "InProgress", "ErrorMessage": null}, "WF_WorkflowTasks": {"TableName": "WF_WorkflowTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.333503+08:00", "LastUpdateTime": "2025-03-05T18:14:47.335887+08:00", "Status": "InProgress", "ErrorMessage": null}, "WF_WorkflowProcesses": {"TableName": "WF_WorkflowProcesses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.352433+08:00", "LastUpdateTime": "2025-03-05T18:14:47.354309+08:00", "Status": "InProgress", "ErrorMessage": null}, "WF_WorkflowSchemeAuths": {"TableName": "WF_WorkflowSchemeAuths", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.356896+08:00", "LastUpdateTime": "2025-03-05T18:14:47.358556+08:00", "Status": "InProgress", "ErrorMessage": null}, "WF_WorkflowStamps": {"TableName": "WF_WorkflowStamps", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.37069+08:00", "LastUpdateTime": "2025-03-05T18:14:47.373589+08:00", "Status": "InProgress", "ErrorMessage": null}, "WF_WorkflowTaskLogs": {"TableName": "WF_WorkflowTaskLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.37684+08:00", "LastUpdateTime": "2025-03-05T18:14:47.379651+08:00", "Status": "InProgress", "ErrorMessage": null}, "WF_WorkflowUnits": {"TableName": "WF_WorkflowUnits", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.468637+08:00", "LastUpdateTime": "2025-03-05T18:14:47.47027+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_DataTableDataAnalyses": {"TableName": "Ybt_DataTableDataAnalyses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.470924+08:00", "LastUpdateTime": "2025-03-05T18:14:47.47235+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_AssociatedConfigs": {"TableName": "Ybt_AssociatedConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.494417+08:00", "LastUpdateTime": "2025-03-05T18:14:47.495757+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_CompanyClaims": {"TableName": "Ybt_CompanyClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.496324+08:00", "LastUpdateTime": "2025-03-05T18:14:47.497864+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_DataTables": {"TableName": "Ybt_DataTables", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.515927+08:00", "LastUpdateTime": "2025-03-05T18:14:47.518029+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_DataTableCompanies": {"TableName": "Ybt_DataTableCompanies", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.516887+08:00", "LastUpdateTime": "2025-03-05T18:14:47.518962+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_Users": {"TableName": "Ybt_Users", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.536804+08:00", "LastUpdateTime": "2025-03-05T18:14:47.544039+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_DataTableDataUpdateLogs": {"TableName": "Ybt_DataTableDataUpdateLogs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.537333+08:00", "LastUpdateTime": "2025-03-05T18:14:47.539965+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_DataTableDepartments": {"TableName": "Ybt_DataTableDepartments", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.558046+08:00", "LastUpdateTime": "2025-03-05T18:14:47.560778+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_DataTableUsers": {"TableName": "Ybt_DataTableUsers", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.558562+08:00", "LastUpdateTime": "2025-03-05T18:14:47.561149+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_DepartmentFavoriteGroups": {"TableName": "Ybt_DepartmentFavoriteGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.580167+08:00", "LastUpdateTime": "2025-03-05T18:14:47.585952+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_DepartmentFavorites": {"TableName": "Ybt_DepartmentFavorites", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.588492+08:00", "LastUpdateTime": "2025-03-05T18:14:47.589908+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_ReportTables": {"TableName": "Ybt_ReportTables", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.627969+08:00", "LastUpdateTime": "2025-03-05T18:14:47.631598+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_FillingConfigs": {"TableName": "Ybt_FillingConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.632585+08:00", "LastUpdateTime": "2025-03-05T18:14:47.634071+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_MenuPermissions": {"TableName": "Ybt_MenuPermissions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.670943+08:00", "LastUpdateTime": "2025-03-05T18:14:47.674622+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_OnLineAnalysisConfigs": {"TableName": "Ybt_OnLineAnalysisConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.67348+08:00", "LastUpdateTime": "2025-03-05T18:14:47.675084+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_OnLineAnalysisEchartConfigs": {"TableName": "Ybt_OnLineAnalysisEchartConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.714301+08:00", "LastUpdateTime": "2025-03-05T18:14:47.718068+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_Positions": {"TableName": "Ybt_Positions", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.720394+08:00", "LastUpdateTime": "2025-03-05T18:14:47.728862+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_PositionClaims": {"TableName": "Ybt_PositionClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.756253+08:00", "LastUpdateTime": "2025-03-05T18:14:47.758976+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_PositionRoles": {"TableName": "Ybt_PositionRoles", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.757388+08:00", "LastUpdateTime": "2025-03-05T18:14:47.759763+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_QuestionFeedbacks": {"TableName": "Ybt_QuestionFeedbacks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.787281+08:00", "LastUpdateTime": "2025-03-05T18:14:47.790082+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_RegionClaims": {"TableName": "Ybt_RegionClaims", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.788406+08:00", "LastUpdateTime": "2025-03-05T18:14:47.790535+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_RegionFavoriteGroups": {"TableName": "Ybt_RegionFavoriteGroups", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.817971+08:00", "LastUpdateTime": "2025-03-05T18:14:47.820114+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_RegionFavorites": {"TableName": "Ybt_RegionFavorites", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.819042+08:00", "LastUpdateTime": "2025-03-05T18:14:47.820566+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_ReportTableRows": {"TableName": "Ybt_ReportTableRows", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.849215+08:00", "LastUpdateTime": "2025-03-05T18:14:47.851463+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_Routes": {"TableName": "Ybt_Routes", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.850376+08:00", "LastUpdateTime": "2025-03-05T18:14:47.852312+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableDataExportRecords": {"TableName": "Ybt_TableDataExportRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.879596+08:00", "LastUpdateTime": "2025-03-05T18:14:47.881864+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableDataImportRecords": {"TableName": "Ybt_TableDataImportRecords", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.88508+08:00", "LastUpdateTime": "2025-03-05T18:14:47.886687+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableDataSetSourceDbTableInfos": {"TableName": "Ybt_TableDataSetSourceDbTableInfos", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.913601+08:00", "LastUpdateTime": "2025-03-05T18:14:47.915079+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableDataSetJoinConfigs": {"TableName": "Ybt_TableDataSetJoinConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.918766+08:00", "LastUpdateTime": "2025-03-05T18:14:47.92048+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableDataSetJoinFieldConfigs": {"TableName": "Ybt_TableDataSetJoinFieldConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.932315+08:00", "LastUpdateTime": "2025-03-05T18:14:47.933891+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableDataTableRuleConfigs": {"TableName": "Ybt_TableDataTableRuleConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.934575+08:00", "LastUpdateTime": "2025-03-05T18:14:47.937153+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableFieldCalculateRules": {"TableName": "Ybt_TableFieldCalculateRules", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.949881+08:00", "LastUpdateTime": "2025-03-05T18:14:47.955592+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableFieldClientSettings": {"TableName": "Ybt_TableFieldClientSettings", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.95089+08:00", "LastUpdateTime": "2025-03-05T18:14:47.956769+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableFiledValidateRules": {"TableName": "Ybt_TableFiledValidateRules", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.968487+08:00", "LastUpdateTime": "2025-03-05T18:14:47.970069+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_TableQueryConfigs": {"TableName": "Ybt_TableQueryConfigs", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.970656+08:00", "LastUpdateTime": "2025-03-05T18:14:47.972121+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_UserDepartments": {"TableName": "Ybt_UserDepartments", "TotalRows": 2, "MigratedRows": 2, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.985394+08:00", "LastUpdateTime": "2025-03-05T18:14:47.992296+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_UserDepartmentPositions": {"TableName": "Ybt_UserDepartmentPositions", "TotalRows": 1, "MigratedRows": 1, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:47.987599+08:00", "LastUpdateTime": "2025-03-05T18:14:47.997551+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_UserPositions": {"TableName": "Ybt_UserPositions", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:48.008091+08:00", "LastUpdateTime": "2025-03-05T18:14:48.009417+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_WFPlanTasks": {"TableName": "Ybt_WFPlanTasks", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:48.014315+08:00", "LastUpdateTime": "2025-03-05T18:14:48.017701+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_WFTaskItems": {"TableName": "Ybt_WFTaskItems", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:48.030175+08:00", "LastUpdateTime": "2025-03-05T18:14:48.031523+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_WFDataProcesses": {"TableName": "Ybt_WFDataProcesses", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:48.03673+08:00", "LastUpdateTime": "2025-03-05T18:14:48.038649+08:00", "Status": "InProgress", "ErrorMessage": null}, "Ybt_WebWidgets": {"TableName": "Ybt_WebWidgets", "TotalRows": 0, "MigratedRows": 0, "SchemaCreated": true, "ForeignKeysCreated": false, "StartTime": "2025-03-05T18:14:48.051582+08:00", "LastUpdateTime": "2025-03-05T18:14:48.052933+08:00", "Status": "InProgress", "ErrorMessage": null}}, "ErrorMessage": null}